import chalk from 'chalk'

enum LoggerLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  DEBUG = 'DEBUG',
  TRACE = 'TRACE',
  SUCCESS = 'SUCCESS'
}

interface LoggerSettings {
  showLogLevel: boolean
  showTimestamp: boolean
  prefix?: string | undefined
}

class Logger {
  private settings: LoggerSettings = {
    showLogLevel: true,
    showTimestamp: true
  }

  constructor(settings: LoggerSettings) {
    this.settings = settings
  }

  info(...args: unknown[]): void {
    this.log(...args)
  }

  warn(...args: unknown[]): void {
    this.log(...args)
  }

  error(...args: unknown[]): void {
    this.log(...args)
  }

  debug(...args: unknown[]): void {
    this.log(...args)
  }

  trace(...args: unknown[]): void {
    const error = new Error()
    const lineNumber = error.stack?.split('\n')[2].split(':')[1]
    const fileName = error.stack?.split('\n')[2].split(':')[0].split(' ')[5]

    this.log(fileName, lineNumber, ...args)
  }

  success(...args: unknown[]): void {
    this.log(...args)
  }

  private log(...args: unknown[]): void {
    console.log(this.formatMessage(...args))
  }

  private formatMessage(...args: unknown[]): string {
    const timestamp = new Date().toISOString()
    const parts = [
      ...(this.settings.showTimestamp ? [`[${chalk.gray(timestamp)}]`] : []),
      ...(this.settings.showLogLevel ? [`[${chalk.gray(LoggerLevel.INFO)}]`] : []),
      ...chalk.white(args)
    ]
    return parts.join(' ')
  }
}

export default Logger
